const { createReceiptsModule } = require("../../Universal/modules/receipts");

// For now, let's use a simple configuration that works with the existing system
// We'll integrate the Universal system later if needed
exports.receipts = {
  name: "Receipts",
  icon: "DollarOutlined",
  path: "/finance/receipts",
  parent: "finance",
  collection: "receipts",
  singular: "Receipt",
  columns: [
    {
      title: "Date",
      dataIndex: "date",
      valueType: "date",
      isRequired: true,
      isPrintable: true,
      noBackDate: true,
      sorter: true,
    },
    {
      dataIndex: "client",
      title: "Client",
      type: "dbSelect",
      collection: "people",
      label: ["name"],
      isRequired: true,
    },
    {
      valueType: "select",
      dataIndex: "pMethod",
      title: "Payment Method",
      width: "lg",
      isRequired: true,
      isPrintable: true,
      valueEnum: {
        cash: { text: "Cash" },
        bank: { text: "Bank Deposit" },
        cheque: { text: "Cheque" },
        mtn: { text: "MTN Mobile Money" },
        airtel: { text: "Airtel Mobile Money" },
        other: { text: "Other" },
      },
    },
    {
      title: "Account",
      dataIndex: "account",
      type: "dbSelect",
      collection: "accounts",
      label: ["name"],
      isPrintable: true,
    },
    {
      valueType: "textarea",
      dataIndex: "particulars",
      title: "Particulars",
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Amount",
      dataIndex: "amount",
      valueType: "money",
      isRequired: true,
      isPrintable: true,
      sorter: true,
    },
    {
      title: "Banked",
      dataIndex: "banked",
      valueType: "switch",
      hideInForm: false,
      isPrintable: false,
      initialValue: false,
    },
    {
      title: "Banking Date",
      dataIndex: "banking_date",
      valueType: "date",
      hideInForm: false,
      isPrintable: false,
      dependency: {
        name: ["banked"],
        condition: (banked) => banked === true,
      },
    },
  ],
};
