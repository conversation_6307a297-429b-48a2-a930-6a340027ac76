import React, { useRef, useState, useEffect } from "react";
import Receipt from "../CustomViews/Receipt/";
import moment from "moment";
import { BetaSchemaForm, TableDropdown } from "@ant-design/pro-components";
import { message, Input, Button } from "antd";
import {
  BankOutlined,
  MessageOutlined,
  EyeOutlined,
  UndoOutlined
} from "@ant-design/icons";
import {
  getSMSTemplatesForCategory,
  sendReceiptSMS,
  getClientPhoneNumber,
  processTemplateVariables,
  isSMSEnabled
} from '../utils/smsUtils';
import { refreshRecordData } from "../../../Utils/RecordRefreshUtility";
import AppDatabase from "../../../Utils/AppDatabase";

export default {
  CustomView: (data) => <Receipt {...data} />,
  print: true,

  afterSave: async (data, { pouchDatabase, databasePrefix, CRUD_USER }) => {
    try {
      // Only proceed if the receipt has an occupancy
      if (data.occupancy && data.occupancy.value) {
        // Get the occupancy document
        const occupancyDoc = await pouchDatabase("occupations", databasePrefix).getDocument(data.occupancy.value);

        if (occupancyDoc) {
          // Get all invoices and receipts for this occupancy using proper database functions
          const [allInvoices, allReceipts] = await Promise.all([
            (await pouchDatabase("invoices", databasePrefix).getAllData())
              .filter(i => i.occupancy && i.occupancy.value === data.occupancy.value),
            (await pouchDatabase("receipts", databasePrefix).getAllData())
              .filter(r => r.occupancy && r.occupancy.value === data.occupancy.value)
          ]);

          // Calculate balances
          const totalInvoices = allInvoices.reduce((total, invoice) => total + (invoice.amount || 0), 0);
          const totalRentInvoices = allInvoices
            .filter(i => i.invoice_type === "Rent")
            .reduce((total, invoice) => total + (invoice.amount || 0), 0);

          const totalReceipts = allReceipts.reduce((total, receipt) => total + (receipt.amount || 0), 0);
          const totalRentReceipts = allReceipts
            .filter(r => r.receipt_type === "Rent")
            .reduce((total, receipt) => total + (receipt.amount || 0), 0);

          // Calculate balance and rent balance
          const balance = totalInvoices - totalReceipts;
          const rentBalance = totalRentInvoices - totalRentReceipts;

          // Update the occupancy document with the new balances
          const updatedOccupancy = {
            ...occupancyDoc,
            balance: balance,
            rent_balance: rentBalance,
            last_payment_date: data.date,
            last_payment_amount: data.amount,
            payments: [
              ...(occupancyDoc.payments || []),
              {
                receipt_id: data._id,
                date: data.date,
                amount: data.amount,
                method: data.pMethod,
                type: data.receipt_type
              }
            ]
          };

          // Save the updated occupancy
          await pouchDatabase("occupations", databasePrefix).saveDocument(updatedOccupancy, CRUD_USER);
          console.log("Occupation record updated with new balance information after receipt creation");
        }
      }
    } catch (error) {
      console.error("Error in receipts afterSave:", error);
    }
  },

  // Add more actions menu for receipts
  MoreActions: (props) => {
    const action = useRef();
    const smsAction = useRef();
    const [smsTemplates, setSmsTemplates] = useState([]);
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const [previewMessage, setPreviewMessage] = useState('');
    const [showPreview, setShowPreview] = useState(false);

    const {
      pouchDatabase,
      databasePrefix,
      record,
      CRUD_USER,
      actionRef,
      updateOptimisticRecord,
      collection,
    } = props;

    // Use the utility function for refreshing record data
    const refreshRecord = async (updatedRecord) => {
      return await refreshRecordData(
        updatedRecord,
        updateOptimisticRecord,
        pouchDatabase,
        collection,
        databasePrefix
      );
    };

    // Get selected branch
    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

    // Load SMS templates on component mount
    useEffect(() => {
      const loadTemplates = async () => {
        try {
          const templates = await getSMSTemplatesForCategory(pouchDatabase, databasePrefix, 'receipt');
          setSmsTemplates(templates);
        } catch (error) {
          console.error('Error loading SMS templates:', error);
        }
      };
      loadTemplates();
    }, [pouchDatabase, databasePrefix]);

    const handleTemplateChange = async (templateId, formRef) => {
      setSelectedTemplate(templateId);
      const template = smsTemplates.find(t => t._id === templateId);
      if (template && record) {
        try {
          // Get client data for preview
          let clientData = null;
          if (record.client && record.client.value) {
            try {
              clientData = await pouchDatabase('people', databasePrefix).getDocument(record.client.value);
            } catch (error) {
              clientData = { name: record.client.label || 'Valued Customer' };
            }
          }

          // Get occupancy data for preview
          let occupancyData = null;
          if (record.occupancy && record.occupancy.value) {
            try {
              occupancyData = await pouchDatabase('occupations', databasePrefix).getDocument(record.occupancy.value);
            } catch (error) {
              console.warn('Could not fetch occupancy data for preview');
            }
          }

          const processedMessage = processTemplateVariables(template.message, record, clientData, occupancyData);
          setPreviewMessage(processedMessage);

          // Update form with processed message
          if (formRef && formRef.current) {
            formRef.current.setFieldsValue({
              message: processedMessage,
              template_id: templateId
            });
          }
        } catch (error) {
          console.error('Error processing template:', error);
        }
      }
    };

    const getInitialPhoneNumber = async () => {
      if (record.client && record.client.value) {
        try {
          const clientData = await pouchDatabase('people', databasePrefix).getDocument(record.client.value);
          return getClientPhoneNumber(clientData) || '';
        } catch (error) {
          console.warn('Could not fetch client phone number:', error);
          return '';
        }
      }
      return '';
    };

    // Create menus array with banking status and reverse receipt actions
    const menus = [
      {
        key: "update_banking_status",
        name: (
          <BetaSchemaForm
            formRef={action}
            submitter={{
              searchConfig: {
                resetText: "Cancel",
                submitText: "Update Banking Status",
              },
            }}
            modalProps={{ centered: true }}
            grid={true}
            trigger={
              <a key="button" type="primary">
                <BankOutlined /> Update Banking Status
              </a>
            }
            title={"Update Receipt Banking Status"}
            destroyOnClose={true}
            layoutType="ModalForm"
            initialValues={{
              banked: record.banked || false,
              banking_date: record.banking_date || null,
            }}
            onFinish={async (values) => {
              try {
                // Update the receipt with banking information
                const updatedRecord = {
                  ...record,
                  banked: values.banked,
                  banking_date: values.banked ? values.banking_date : null,
                };

                await pouchDatabase(
                  "receipts",
                  databasePrefix
                ).saveDocument(updatedRecord, CRUD_USER);

                // Refresh receipt record to show updated status
                await refreshRecord(updatedRecord);

                message.success("Banking status updated successfully");

                // Refresh the table
                if (actionRef.current) {
                  actionRef.current.reload();
                }

                return true;
              } catch (error) {
                console.error("Error updating banking status:", error);
                message.error("Failed to update banking status");
                return false;
              }
            }}
            columns={[
              {
                valueType: "switch",
                dataIndex: "banked",
                title: "Banked",
                colProps: {
                  md: 24,
                },
                fieldProps: {
                  checkedChildren: "Yes",
                  unCheckedChildren: "No",
                },
              },
              {
                valueType: "dependency",
                name: ["banked"],
                columns: ({ banked }) => {
                  if (!banked) return [];
                  return [
                    {
                      valueType: "date",
                      dataIndex: "banking_date",
                      title: "Banking Date",
                      isRequired: true,
                      colProps: {
                        md: 24,
                      },
                      fieldProps: {
                        placeholder: "Select banking date",
                      },
                    },
                  ];
                },
              },
            ]}
          />
        ),
      },
      {
        key: "reverse_receipt",
        name: (
          <a
            key="button"
            type="primary"
            onClick={() => {
              Modal.confirm({
                title: 'Reverse Receipt',
                content: `Are you sure you want to reverse this receipt? This will create a new receipt with negative amount (-${Math.abs(record.amount || 0)}) and append the original receipt ID [${record._id}] to the particulars.`,
                okText: 'Yes, Reverse Receipt',
                cancelText: 'Cancel',
                onOk: async () => {
                  try {
                    // Create the reversal receipt with identical data but negative amount
                    const reversalReceipt = {
                      date: new Date(),
                      client: record.client,
                      pMethod: record.pMethod,
                      account: record.account,
                      particulars: `${record.particulars} [${record._id}]`,
                      amount: -Math.abs(record.amount || 0), // Ensure negative amount
                      occupancy: record.occupancy,
                      receipt_type: record.receipt_type,
                      banked: false, // Default to not banked for reversal
                      banking_date: null,
                      reversed_from: {
                        value: record._id,
                        label: `Original Receipt (${record._id})`
                      },
                      is_reversal: true,
                    };

                    await pouchDatabase(
                      "receipts",
                      databasePrefix
                    ).saveDocument(reversalReceipt, CRUD_USER);

                    message.success("Reversal receipt created successfully");

                    // Refresh the table
                    if (actionRef.current) {
                      actionRef.current.reload();
                    }
                  } catch (error) {
                    console.error("Error creating reversal receipt:", error);
                    message.error("Failed to create reversal receipt");
                  }
                },
              });
            }}
          >
            <UndoOutlined /> Reverse Receipt
          </a>
        ),
      },
    ];

    // Add SMS option if SMS is enabled
    if (isSMSEnabled()) {
      menus.push({
        key: "send_sms",
        name: (
          <BetaSchemaForm
            formRef={smsAction}
            submitter={{
              searchConfig: {
                resetText: "Cancel",
                submitText: "Send SMS",
              },
            }}
            modalProps={{ centered: true, width: 600 }}
            grid={true}
            trigger={
              <a key="button" type="primary">
                <MessageOutlined /> Send Receipt SMS
              </a>
            }
            title={"Send Receipt SMS Notification"}
            destroyOnClose={true}
            layoutType="ModalForm"
            request={async () => {
              const phoneNumber = await getInitialPhoneNumber();
              return { phone_number: phoneNumber };
            }}
            onFinish={async (values) => {
              try {
                const result = await sendReceiptSMS(
                  record,
                  values.phone_number,
                  values.message,
                  pouchDatabase,
                  databasePrefix,
                  CRUD_USER
                );

                if (result.success) {
                  message.success(result.message);
                } else {
                  message.error(result.message);
                }

                return result.success;
              } catch (error) {
                console.error("Error sending SMS:", error);
                message.error("Failed to send SMS");
                return false;
              }
            }}
            columns={[
              {
                valueType: "select",
                dataIndex: "template_id",
                title: "SMS Template",
                colProps: { md: 24 },
                fieldProps: {
                  placeholder: "Select an SMS template",
                  onChange: (value) => handleTemplateChange(value, smsAction),
                  options: smsTemplates.map(template => ({
                    label: template.name,
                    value: template._id
                  }))
                }
              },
              {
                valueType: "text",
                dataIndex: "phone_number",
                title: "Phone Number",
                isRequired: true,
                colProps: { md: 24 },
                fieldProps: {
                  placeholder: "Enter phone number (e.g., 0700123456)"
                }
              },
              {
                valueType: "textarea",
                dataIndex: "message",
                title: "Message",
                isRequired: true,
                colProps: { md: 24 },
                fieldProps: {
                  rows: 4,
                  placeholder: "SMS message content...",
                  showCount: true,
                  maxLength: 160
                },
                renderFormItem: (_, { value, onChange }) => (
                  <div>
                    <Input.TextArea
                      value={value}
                      onChange={onChange}
                      rows={4}
                      placeholder="SMS message content..."
                      showCount
                      maxLength={160}
                    />
                    {previewMessage && (
                      <Button
                        type="link"
                        icon={<EyeOutlined />}
                        onClick={() => setShowPreview(true)}
                        style={{ padding: 0, marginTop: 8 }}
                      >
                        Preview Message
                      </Button>
                    )}
                  </div>
                )
              }
            ]}
          />
        ),
      });
    }

    return (
      <TableDropdown
        key="actionGroup"
        menus={menus}
      />
    );
  },
  statistics: async function receiptsStatistics(databasePrefix = '') {
    try {
      const receiptsDb = AppDatabase("receipts", databasePrefix);

      // Get all receipts
      const allReceipts = await receiptsDb.getAllData();

      // Calculate total amount from all receipts
      const totalAmount = allReceipts.reduce((sum, receipt) =>
        sum + (parseFloat(receipt.amount) || 0), 0);

      // Get current month receipts
      const currentMonth = moment().startOf('month');
      const currentMonthReceipts = allReceipts.filter(receipt =>
        moment(receipt.date).isSameOrAfter(currentMonth));
      const currentMonthTotal = currentMonthReceipts.reduce((sum, receipt) =>
        sum + (parseFloat(receipt.amount) || 0), 0);

      // Get previous month receipts
      const prevMonth = moment().subtract(1, 'months').startOf('month');
      const prevMonthEnd = moment().startOf('month').subtract(1, 'day');
      const prevMonthReceipts = allReceipts.filter(receipt =>
        moment(receipt.date).isSameOrAfter(prevMonth) &&
        moment(receipt.date).isSameOrBefore(prevMonthEnd));
      const prevMonthTotal = prevMonthReceipts.reduce((sum, receipt) =>
        sum + (parseFloat(receipt.amount) || 0), 0);

      // Get today's receipts
      const today = moment().startOf('day');
      const todayReceipts = allReceipts.filter(receipt =>
        moment(receipt.date).isSameOrAfter(today));
      const todayTotal = todayReceipts.reduce((sum, receipt) =>
        sum + (parseFloat(receipt.amount) || 0), 0);

      // Group receipts by payment method
      const paymentMethodCounts = {};
      allReceipts.forEach(receipt => {
        const method = receipt.pMethod || 'other';
        paymentMethodCounts[method] = (paymentMethodCounts[method] || 0) + 1;
      });

      // Find most common payment method
      let mostCommonMethod = 'other';
      let maxCount = 0;
      Object.entries(paymentMethodCounts).forEach(([method, count]) => {
        if (count > maxCount) {
          mostCommonMethod = method;
          maxCount = count;
        }
      });

      // Format the most common method name for display
      const methodDisplayNames = {
        cash: 'Cash',
        bank: 'Bank Deposit',
        cheque: 'Cheque',
        mtn: 'MTN Mobile Money',
        airtel: 'Airtel Mobile Money',
        other: 'Other'
      };

      return [
        { title: "Total Receipts", value: allReceipts.length },
        { title: "Total Amount", value: totalAmount, valueType: "money" },
        { title: "This Month", value: currentMonthTotal, valueType: "money" },
        { title: "Last Month", value: prevMonthTotal, valueType: "money" },
        { title: "Today", value: todayTotal, valueType: "money" },
        { title: "Popular Method", value: methodDisplayNames[mostCommonMethod] || mostCommonMethod }
      ];
    } catch (error) {
      console.error("Error calculating receipt statistics:", error);
      return [
        { title: "Total Receipts", value: 0 },
        { title: "Total Amount", value: 0, valueType: "money" },
        { title: "This Month", value: 0, valueType: "money" },
        { title: "Last Month", value: 0, valueType: "money" },
        { title: "Today", value: 0, valueType: "money" },
        { title: "Popular Method", value: "N/A" }
      ];
    }
  }
};
